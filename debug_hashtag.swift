import Foundation

// Test the full parsing logic for error patterns
let errorPatterns = ["error", "unknown", "null", "undefined", "none", "n/a"]

// First test the regex pattern specifically for "n/a"
let testMessage = "#n/a Test message"
let regexPattern = #"^#([a-zA-Z0-9\-_]+(?:/[a-zA-Z0-9\-_]+)?)\s+(.+)$"#

print("Testing regex for 'n/a':")
print("Message: '\(testMessage)'")
print("Pattern: \(regexPattern)")

do {
    let regex = try NSRegularExpression(pattern: regexPattern, options: [])
    if let match = regex.firstMatch(in: testMessage, options: [], range: NSRange(location: 0, length: testMessage.count)) {
        let categoryPart = String(testMessage[Range(match.range(at: 1), in: testMessage)!])
        print("Regex matched! Category part: '\(categoryPart)'")
    } else {
        print("Regex did not match!")
    }
} catch {
    print("Regex error: \(error)")
}

print("\n" + String(repeating: "-", count: 40) + "\n")

// Simulate the full parsing logic
func parseMessage(_ message: String) -> (categoryPath: String?, cleanedMessage: String) {
    let trimmedMessage = message.trimmingCharacters(in: .whitespacesAndNewlines)

    // Check for hashtag at the beginning
    let pattern = #"^#([a-zA-Z0-9\-_]+(?:/[a-zA-Z0-9\-_]+)?)\s+(.+)$"#

    do {
        let regex = try NSRegularExpression(pattern: pattern, options: [])
        if let match = regex.firstMatch(in: trimmedMessage, options: [], range: NSRange(location: 0, length: trimmedMessage.count)) {
            let categoryPathString = String(trimmedMessage[Range(match.range(at: 1), in: trimmedMessage)!])
            let remainingMessage = String(trimmedMessage[Range(match.range(at: 2), in: trimmedMessage)!])

            // Validate category name
            func isValidCategoryName(_ name: String) -> Bool {
                let trimmed = name.trimmingCharacters(in: .whitespacesAndNewlines)

                // Check length (1-50 characters)
                guard trimmed.count >= 1 && trimmed.count <= 50 else {
                    return false
                }

                // Check for valid characters
                let allowedCharacterSet = CharacterSet.alphanumerics.union(.whitespaces).union(CharacterSet(charactersIn: "-_"))
                guard trimmed.rangeOfCharacter(from: allowedCharacterSet.inverted) == nil else {
                    return false
                }

                // Avoid common error patterns
                let lowercased = trimmed.lowercased()
                let invalidPatterns = ["error", "unknown", "null", "undefined", "none", "n/a"]

                return !invalidPatterns.contains(lowercased)
            }

            if isValidCategoryName(categoryPathString) {
                return (categoryPath: categoryPathString, cleanedMessage: remainingMessage)
            } else {
                return (categoryPath: nil, cleanedMessage: trimmedMessage)
            }
        }
    } catch {
        print("Regex error: \(error)")
    }

    return (categoryPath: nil, cleanedMessage: trimmedMessage)
}

for pattern in errorPatterns {
    let message = "#\(pattern) Test message"
    let result = parseMessage(message)
    print("\nTesting: '\(message)'")
    print("Category path: \(result.categoryPath ?? "nil")")
    print("Should be nil: \(result.categoryPath == nil)")
}
